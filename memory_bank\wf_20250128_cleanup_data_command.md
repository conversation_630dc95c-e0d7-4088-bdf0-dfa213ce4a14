# Workflow: Tạo Artisan Command Cleanup Data For Test

**Ngày tạo:** 2025-01-28  
**M<PERSON><PERSON> đích:** <PERSON><PERSON><PERSON> command để làm sạch dữ liệu production cho môi trường test

## 📋 Yêu cầu đã thực hiện

### ✅ Tính năng an toàn
- [x] Kiểm tra database name không chứa "prod" (case-insensitive)
- [x] Kiểm tra APP_ENV không chứa "prod" (case-insensitive)  
- [x] Hiển thị cảnh báo và yêu cầu xác nhận
- [x] Option `--force` để skip confirmation
- [x] Log tất cả thao tác xóa để audit
- [x] Sử dụng database transaction

### ✅ Quy tắc xóa dữ liệu
1. **Jobs**: Giữ lại 5 jobs active có submit_cvs, xóa tất cả còn lại
2. **Companies & Employers**: Giữ lại 5 companies và employer users sở hữu jobs được giữ lại
3. **Rec Users**: Giữ lại 5 rec users đã submit CV vào jobs được giữ lại
4. **Warehouse CVs**: Giữ lại 10 warehouse_cvs có liên kết selling_buys, xóa orphaned data
5. **Xóa hoàn toàn**: itnavi_*, audits, user_infos, email_logs
6. **Orphaned data**: Xóa submit_cv_metas không có submit_cv_id tồn tại

### ✅ Tính năng kỹ thuật
- [x] Progress reporting với số lượng records bị xóa
- [x] Error handling với rollback transaction
- [x] Comprehensive logging
- [x] Safety checks trước khi thực thi

## 📁 Files đã tạo

### `app/Console/Commands/CleanupDataForTest.php`
- Command signature: `data:cleanup-for-test {--force}`
- Các phương thức chính:
  - `performSafetyChecks()`: Kiểm tra an toàn
  - `getConfirmation()`: Xác nhận từ user
  - `cleanupJobs()`: Xóa jobs
  - `cleanupCompaniesAndEmployers()`: Xóa companies và employers
  - `cleanupRecUsers()`: Xóa rec users
  - `cleanupWarehouseCvs()`: Xóa warehouse CVs
  - `cleanupCompleteTables()`: Xóa hoàn toàn các bảng
  - `cleanupOrphanedData()`: Xóa dữ liệu orphaned

## 🚀 Cách sử dụng

### Chạy với confirmation prompt:
```powershell
php artisan data:cleanup-for-test
```

### Chạy với force (skip confirmation):
```powershell
php artisan data:cleanup-for-test --force
```

## 🔒 Biện pháp an toàn

1. **Database Safety**: Không chạy được trên database có tên chứa "prod"
2. **Environment Safety**: Không chạy được khi APP_ENV chứa "prod"
3. **User Confirmation**: Yêu cầu xác nhận trước khi thực thi (trừ khi dùng --force)
4. **Transaction Safety**: Tất cả thao tác trong transaction, rollback nếu có lỗi
5. **Audit Trail**: Log tất cả thao tác để có thể audit sau này

## 📊 Output mẫu

```
✅ Safety checks passed:
   Database: recland_test
   Environment: testing

⚠️  WARNING: This operation will permanently delete data from the database!
   This action cannot be undone.

Are you sure you want to proceed with data cleanup? (yes/no) [no]:
> yes

Starting data cleanup for test environment...

🔄 Cleaning up jobs...
   Jobs to keep: 5
   ✅ Deleted 1247 jobs

🔄 Cleaning up companies and employers...
   Companies to keep: 5
   ✅ Deleted 892 companies
   ✅ Deleted 456 employer users

🔄 Cleaning up rec users...
   Rec users to keep: 5
   ✅ Deleted 2341 rec users

🔄 Cleaning up warehouse CVs...
   Warehouse CVs to keep: 10
   ✅ Deleted 5678 warehouse CVs
   ✅ Deleted 234 orphaned selling buys

🔄 Cleaning up complete tables...
   ✅ Deleted 12345 records from itnavi_search_cvs
   ✅ Deleted 6789 records from itnavi_users
   ✅ Deleted 9876 records from itnavi_user_cvs
   ✅ Deleted 543 records from itnavi_user_external_sources
   ✅ Deleted 23456 records from audits
   ✅ Deleted 7890 records from user_infos
   ✅ Deleted 12345 records from email_logs
   ✅ Total deleted from complete tables: 73244

🔄 Cleaning up orphaned data...
   ✅ Deleted 123 orphaned submit_cv_metas

✅ Data cleanup completed successfully!
```

## 🔍 Kiểm tra sau khi chạy

1. Kiểm tra logs trong `storage/logs/laravel.log`
2. Verify số lượng records còn lại trong các bảng chính
3. Test functionality với dataset nhỏ gọn
4. Backup database test sau khi cleanup để tái sử dụng

## ⚠️ Lưu ý quan trọng

- Command này chỉ dành cho môi trường test/development
- Luôn backup database trước khi chạy
- Không thể undo sau khi chạy thành công
- Kiểm tra kỹ database name và environment trước khi chạy
