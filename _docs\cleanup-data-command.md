# Data Cleanup Command Documentation

## Tổng quan

Command `data:cleanup-for-test` đ<PERSON><PERSON><PERSON> thiết kế để làm sạch dữ liệu production và tạo ra một dataset nhỏ gọn phục vụ cho việc testing. Command này có các biện pháp an toàn nghiêm ngặt để tránh chạy nhầm trên môi trường production.

## C<PERSON> pháp

```bash
php artisan data:cleanup-for-test [--force]
```

### Options

- `--force`: Bỏ qua confirmation prompt và chạy trực tiếp

## Biện pháp an toàn

### 1. Database Name Check
Command sẽ từ chối chạy nếu tên database chứa từ "prod" (case-insensitive).

### 2. Environment Check  
Command sẽ từ chối chạy nếu `APP_ENV` chứa từ "prod" (case-insensitive).

### 3. User Confirmation
Hiển thị cảnh báo và yêu cầu xá<PERSON> nhận từ user trước khi thực thi (trừ khi dùng `--force`).

### 4. Transaction Safety
Tất cả thao tác được thực hiện trong database transaction. Nếu có lỗi, toàn bộ thay đổi sẽ được rollback.

### 5. Comprehensive Logging
Tất cả thao tác được log vào `storage/logs/laravel.log` để có thể audit.

## Quy tắc xóa dữ liệu

### 1. Jobs Cleanup
- **Giữ lại**: 5 jobs có `status = 'active'` và có ít nhất 1 record trong `submit_cvs`
- **Xóa**: Tất cả jobs khác

### 2. Companies & Employers Cleanup
- **Giữ lại**: 5 companies sở hữu các jobs được giữ lại ở bước 1
- **Giữ lại**: Users có role employer sở hữu các companies được giữ lại
- **Xóa**: Tất cả companies và employer users khác

### 3. Rec Users Cleanup
- **Giữ lại**: 5 users có `type = 'rec'` đã submit CV vào các jobs được giữ lại
- **Xóa**: Tất cả rec users khác

### 4. Warehouse CVs Cleanup
- **Giữ lại**: 10 warehouse_cvs có liên kết trong `warehouse_cv_selling_buys`
- **Xóa**: Tất cả warehouse_cvs khác
- **Dọn dẹp**: Xóa records trong `warehouse_cv_selling_buys` có `warehouse_cv_id` không tồn tại

### 5. Complete Table Cleanup
Xóa hoàn toàn tất cả dữ liệu trong các bảng:
- `itnavi_search_cvs`
- `itnavi_users`
- `itnavi_user_cvs`
- `itnavi_user_external_sources`
- `audits`
- `user_infos`
- `email_logs`

### 6. Orphaned Data Cleanup
- Xóa records trong `submit_cv_metas` có `submit_cv_id` không tồn tại trong `submit_cvs`

## Ví dụ sử dụng

### Chạy với confirmation prompt
```bash
php artisan data:cleanup-for-test
```

Output:
```
✅ Safety checks passed:
   Database: recland_test
   Environment: testing

⚠️  WARNING: This operation will permanently delete data from the database!
   This action cannot be undone.

Are you sure you want to proceed with data cleanup? (yes/no) [no]:
> yes

Starting data cleanup for test environment...
🔄 Cleaning up jobs...
   Jobs to keep: 5
   ✅ Deleted 1247 jobs
...
✅ Data cleanup completed successfully!
```

### Chạy với force option
```bash
php artisan data:cleanup-for-test --force
```

## Error Handling

### Safety Check Failures
```bash
❌ Safety check failed: Database name contains "prod". Cannot run on production database.
```

```bash
❌ Safety check failed: APP_ENV contains "prod". Cannot run in production environment.
```

### Runtime Errors
Nếu có lỗi trong quá trình thực thi, transaction sẽ được rollback và hiển thị thông báo lỗi:

```bash
❌ Error during cleanup: [Error message]
```

## Logging

Tất cả thao tác được log với các level khác nhau:

### Info Logs
- Bắt đầu và kết thúc cleanup
- Số lượng records được xóa từ mỗi bảng

### Warning Logs  
- Không thể xóa một bảng nào đó

### Error Logs
- Lỗi trong quá trình cleanup
- Stack trace của lỗi

## Best Practices

### 1. Backup trước khi chạy
```bash
mysqldump -u username -p database_name > backup_before_cleanup.sql
```

### 2. Kiểm tra environment
Đảm bảo đang chạy trên môi trường test/development:
```bash
php artisan env
```

### 3. Kiểm tra database name
```bash
php artisan tinker
>>> DB::connection()->getDatabaseName()
```

### 4. Test sau khi cleanup
- Kiểm tra functionality cơ bản
- Verify số lượng records còn lại
- Test các tính năng chính

### 5. Tạo backup sau cleanup
Sau khi cleanup thành công, tạo backup để tái sử dụng:
```bash
mysqldump -u username -p database_name > test_dataset_clean.sql
```

## Troubleshooting

### Command không xuất hiện trong artisan list
```bash
composer dump-autoload
php artisan clear-compiled
```

### Permission errors
Đảm bảo user có quyền DELETE trên database.

### Memory issues với dataset lớn
Command sử dụng chunking và transaction để xử lý dataset lớn một cách hiệu quả.

## Lưu ý quan trọng

⚠️ **CẢNH BÁO**: Command này sẽ xóa vĩnh viễn dữ liệu từ database. Không thể undo sau khi thực thi thành công.

- Chỉ sử dụng trên môi trường test/development
- Luôn backup database trước khi chạy  
- Kiểm tra kỹ database name và environment
- Test thoroughly sau khi cleanup
