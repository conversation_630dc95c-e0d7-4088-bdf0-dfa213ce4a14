<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Exception;

class CleanupDataForTest extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'data:cleanup-for-test {--force : Skip confirmation prompt}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Clean up production data to create a smaller dataset for testing';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        // Safety checks
        if (!$this->performSafetyChecks()) {
            return Command::FAILURE;
        }

        // Show warning and get confirmation
        if (!$this->getConfirmation()) {
            $this->info('Operation cancelled.');
            return Command::SUCCESS;
        }

        $this->info('Starting data cleanup for test environment...');
        Log::info('Data cleanup for test started', ['user' => auth()->user()->id ?? 'console']);

        try {
            DB::beginTransaction();

            $this->cleanupJobs();
            $this->cleanupCompaniesAndEmployers();
            $this->cleanupRecUsers();
            $this->cleanupWarehouseCvs();
            $this->cleanupCompleteTables();
            $this->cleanupOrphanedData();

            DB::commit();
            
            $this->info('✅ Data cleanup completed successfully!');
            Log::info('Data cleanup for test completed successfully');
            
            return Command::SUCCESS;
            
        } catch (Exception $e) {
            DB::rollBack();
            $this->error('❌ Error during cleanup: ' . $e->getMessage());
            Log::error('Data cleanup failed', ['error' => $e->getMessage(), 'trace' => $e->getTraceAsString()]);
            
            return Command::FAILURE;
        }
    }

    /**
     * Perform safety checks before running cleanup
     */
    private function performSafetyChecks(): bool
    {
        $dbName = DB::connection()->getDatabaseName();
        $appEnv = config('app.env');

        // Check database name
        if (stripos($dbName, 'prod') !== false) {
            $this->error('❌ Safety check failed: Database name contains "prod". Cannot run on production database.');
            return false;
        }

        // Check app environment
        if (stripos($appEnv, 'prod') !== false) {
            $this->error('❌ Safety check failed: APP_ENV contains "prod". Cannot run in production environment.');
            return false;
        }

        $this->info("✅ Safety checks passed:");
        $this->line("   Database: {$dbName}");
        $this->line("   Environment: {$appEnv}");

        return true;
    }

    /**
     * Get user confirmation before proceeding
     */
    private function getConfirmation(): bool
    {
        if ($this->option('force')) {
            return true;
        }

        $this->warn('⚠️  WARNING: This operation will permanently delete data from the database!');
        $this->warn('   This action cannot be undone.');
        
        return $this->confirm('Are you sure you want to proceed with data cleanup?');
    }

    /**
     * Clean up jobs - keep only 5 active jobs with submit_cvs
     */
    private function cleanupJobs(): void
    {
        $this->info('🔄 Cleaning up jobs...');
        
        // Get jobs to keep
        $jobsToKeep = DB::table('job')
            ->select('job.id')
            ->join('submit_cvs', 'job.id', '=', 'submit_cvs.job_id')
            ->where('job.is_active', 1)
            ->groupBy('job.id')
            ->limit(5)
            ->pluck('id');

        $this->line("   Jobs to keep: " . $jobsToKeep->count());

        // Delete jobs not in the keep list
        $deletedCount = DB::table('job')
            ->whereNotIn('id', $jobsToKeep)
            ->delete();

        $this->line("   ✅ Deleted {$deletedCount} jobs");
        Log::info("Jobs cleanup completed", ['kept' => $jobsToKeep->count(), 'deleted' => $deletedCount]);
    }

    /**
     * Clean up companies and employer users
     */
    private function cleanupCompaniesAndEmployers(): void
    {
        $this->info('🔄 Cleaning up companies and employers...');
        
        // Get remaining job IDs after cleanup
        $remainingJobIds = DB::table('job')->pluck('id');
        
        // Get companies to keep (those owning remaining jobs)
        $companiesToKeep = DB::table('companies')
            ->select('companies.id')
            ->join('job', 'companies.id', '=', 'job.company_id')
            ->whereIn('job.id', $remainingJobIds)
            ->distinct()
            ->limit(5)
            ->pluck('id');

        $this->line("   Companies to keep: " . $companiesToKeep->count());

        // Get employer users to keep
        $employersToKeep = DB::table('users')
            ->select('users.id')
            ->join('companies', 'users.company_id', '=', 'companies.id')
            ->whereIn('companies.id', $companiesToKeep)
            ->pluck('id');

        // Delete companies not in keep list
        $deletedCompanies = DB::table('companies')
            ->whereNotIn('id', $companiesToKeep)
            ->delete();

        // Delete employer users not in keep list
        $deletedEmployers = DB::table('users')
            // ->join('user_role', 'users.id', '=', 'user_role.user_id')
            // ->join('roles', 'user_role.role_id', '=', 'roles.id')
            ->where('users.type', 'employer')
            ->whereNotIn('users.id', $employersToKeep)
            ->delete();

        $this->line("   ✅ Deleted {$deletedCompanies} companies");
        $this->line("   ✅ Deleted {$deletedEmployers} employer users");
        
        Log::info("Companies and employers cleanup completed", [
            'companies_kept' => $companiesToKeep->count(),
            'companies_deleted' => $deletedCompanies,
            'employers_deleted' => $deletedEmployers
        ]);
    }

    /**
     * Clean up rec users
     */
    private function cleanupRecUsers(): void
    {
        $this->info('🔄 Cleaning up rec users...');

        // Get remaining job IDs
        $remainingJobIds = DB::table('job')->pluck('id');

        // Get rec users to keep (those who submitted CVs to remaining jobs)
        $recUsersToKeep = DB::table('users')
            ->select('users.id')
            ->join('submit_cvs', 'users.id', '=', 'submit_cvs.user_id')
            ->whereIn('submit_cvs.job_id', $remainingJobIds)
            ->where('users.type', 'rec')
            ->distinct()
            ->limit(5)
            ->pluck('id');

        $this->line("   Rec users to keep: " . $recUsersToKeep->count());

        // Delete rec users not in keep list
        $deletedRecUsers = DB::table('users')
            ->where('type', 'rec')
            ->whereNotIn('id', $recUsersToKeep)
            ->delete();

        $this->line("   ✅ Deleted {$deletedRecUsers} rec users");

        Log::info("Rec users cleanup completed", [
            'kept' => $recUsersToKeep->count(),
            'deleted' => $deletedRecUsers
        ]);
    }

    /**
     * Clean up warehouse CVs
     */
    private function cleanupWarehouseCvs(): void
    {
        $this->info('🔄 Cleaning up warehouse CVs...');

        // Get warehouse CVs to keep (those with selling buys)
        $warehouseCvsToKeep = DB::table('warehouse_cvs')
            ->select('warehouse_cvs.id')
            ->join('warehouse_cv_sellings', 'warehouse_cvs.id', '=', 'warehouse_cv_sellings.warehouse_cv_id')
            ->distinct()
            ->limit(10)
            ->pluck('id');

        $this->line("   Warehouse CVs to keep: " . $warehouseCvsToKeep->count());

        // Delete warehouse CVs not in keep list
        $deletedWarehouseCvs = DB::table('warehouse_cvs')
            ->whereNotIn('id', $warehouseCvsToKeep)
            ->delete();

        // Clean up orphaned warehouse_cv_selling_buys
        $deletedSellingBuys = DB::table('warehouse_cv_sellings')
            ->whereNotIn('warehouse_cv_id', $warehouseCvsToKeep)
            ->delete();

        $this->line("   ✅ Deleted {$deletedWarehouseCvs} warehouse CVs");
        $this->line("   ✅ Deleted {$deletedSellingBuys} orphaned selling buys");

        Log::info("Warehouse CVs cleanup completed", [
            'cvs_kept' => $warehouseCvsToKeep->count(),
            'cvs_deleted' => $deletedWarehouseCvs,
            'selling_buys_deleted' => $deletedSellingBuys
        ]);
    }

    /**
     * Clean up complete tables
     */
    private function cleanupCompleteTables(): void
    {
        $this->info('🔄 Cleaning up complete tables...');

        $tablesToCleanup = [
            'itnavi_search_cvs',
            'itnavi_users',
            'itnavi_user_cvs',
            'itnavi_user_external_sources',
            'audits',
            'user_infos',
            'warehouse_cv_selling_buys',
            'email_logs'
        ];

        $totalDeleted = 0;

        foreach ($tablesToCleanup as $table) {
            try {
                $deletedCount = DB::table($table)->delete();
                $totalDeleted += $deletedCount;
                $this->line("   ✅ Deleted {$deletedCount} records from {$table}");

                Log::info("Table cleanup completed", [
                    'table' => $table,
                    'deleted' => $deletedCount
                ]);
            } catch (Exception $e) {
                $this->warn("   ⚠️  Could not clean table {$table}: " . $e->getMessage());
                Log::warning("Table cleanup failed", [
                    'table' => $table,
                    'error' => $e->getMessage()
                ]);
            }
        }

        $this->line("   ✅ Total deleted from complete tables: {$totalDeleted}");
    }

    /**
     * Clean up orphaned data
     */
    private function cleanupOrphanedData(): void
    {
        $this->info('🔄 Cleaning up orphaned data...');

        // Clean up submit_cv_metas with non-existent submit_cv_id
        $deletedMetas = DB::table('submit_cv_metas')
            ->whereNotExists(function ($query) {
                $query->select(DB::raw(1))
                    ->from('submit_cvs')
                    ->whereColumn('submit_cvs.id', 'submit_cv_metas.submit_cv_id');
            })
            ->delete();

        $this->line("   ✅ Deleted {$deletedMetas} orphaned submit_cv_metas");

        Log::info("Orphaned data cleanup completed", [
            'submit_cv_metas_deleted' => $deletedMetas
        ]);
    }
}
